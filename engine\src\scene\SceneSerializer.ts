/**
 * 场景序列化器
 * 用于序列化和反序列化场景
 */
import * as THREE from 'three';
import { Scene } from './Scene';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import type { World } from '../core/World';
import { Skybox, SkyboxType } from './Skybox';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 场景序列化选项
 */
export interface SceneSerializeOptions {
  /** 是否包含实体 */
  includeEntities?: boolean;
  /** 是否包含组件 */
  includeComponents?: boolean;
  /** 是否包含天空盒 */
  includeSkybox?: boolean;
  /** 是否包含环境光 */
  includeAmbientLight?: boolean;
  /** 是否包含雾效 */
  includeFog?: boolean;
  /** 是否美化JSON输出 */
  prettyPrint?: boolean;
  /** 是否包含元数据 */
  includeMetadata?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 场景反序列化选项
 */
export interface SceneDeserializeOptions {
  /** 是否包含实体 */
  includeEntities?: boolean;
  /** 是否包含组件 */
  includeComponents?: boolean;
  /** 是否包含天空盒 */
  includeSkybox?: boolean;
  /** 是否包含环境光 */
  includeAmbientLight?: boolean;
  /** 是否包含雾效 */
  includeFog?: boolean;
  /** 是否保留现有实体 */
  keepExistingEntities?: boolean;
  /** 是否合并到现有场景 */
  mergeWithExisting?: boolean;
  /** 组件反序列化器映射 */
  componentDeserializers?: Map<string, (data: any, entity: Entity) => Component | null>;
}

/**
 * 场景序列化数据
 */
export interface SceneSerializedData {
  /** 版本 */
  version: string;
  /** 场景ID */
  id: string;
  /** 场景名称 */
  name: string;
  /** 实体数据 */
  entities?: any[];
  /** 天空盒数据 */
  skybox?: any;
  /** 环境光数据 */
  ambientLight?: any;
  /** 雾效数据 */
  fog?: any;
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 场景序列化器
 */
export class SceneSerializer extends EventEmitter {
  /** 当前版本 */
  private static readonly VERSION: string = '1.0.0';

  /** 世界实例 */
  private world: World | null = null;

  /** 组件序列化器映射 */
  private componentSerializers: Map<string, (component: Component) => any> = new Map();

  /** 组件反序列化器映射 */
  private componentDeserializers: Map<string, (data: any, entity: Entity) => Component | null> = new Map();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景序列化器实例
   * @param world 世界实例
   */
  constructor(world?: World) {
    super();

    if (world) {
      this.world = world;
    }
  }

  /**
   * 初始化场景序列化器
   * @param world 世界实例
   */
  public initialize(world?: World): void {
    if (this.initialized) {
      return;
    }

    if (world) {
      this.world = world;
    }

    // 注册默认组件序列化器
    this.registerDefaultComponentSerializers();

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 注册默认组件序列化器
   */
  private registerDefaultComponentSerializers(): void {
    // 注册变换组件序列化器
    this.registerComponentSerializer('TransformComponent', (component: Component) => {
      const transform = component as any;

      return {
        position: transform.getPosition().toArray(),
        rotation: transform.getRotation().toArray(),
        scale: transform.getScale().toArray()
      };
    });

    // 注册变换组件反序列化器
    this.registerComponentDeserializer('TransformComponent', (data: any, entity: Entity) => {
      const transform = entity.getTransform();

      if (transform) {
        if (data.position) {
          transform.setPosition(data.position[0], data.position[1], data.position[2]);
        }

        if (data.rotation) {
          transform.setRotation(data.rotation[0], data.rotation[1], data.rotation[2]);
        }

        if (data.scale) {
          transform.setScale(data.scale[0], data.scale[1], data.scale[2]);
        }
      }

      return transform;
    });
  }

  /**
   * 注册组件序列化器
   * @param componentType 组件类型
   * @param serializer 序列化器函数
   */
  public registerComponentSerializer(
    componentType: string,
    serializer: (component: Component) => any
  ): void {
    this.componentSerializers.set(componentType, serializer);
  }

  /**
   * 注册组件反序列化器
   * @param componentType 组件类型
   * @param deserializer 反序列化器函数
   */
  public registerComponentDeserializer(
    componentType: string,
    deserializer: (data: any, entity: Entity) => Component | null
  ): void {
    this.componentDeserializers.set(componentType, deserializer);
  }

  /**
   * 序列化场景
   * @param scene 场景实例
   * @param options 序列化选项
   * @returns 序列化数据
   */
  public serializeScene(scene: Scene, options: SceneSerializeOptions = {}): SceneSerializedData {
    // 合并选项
    const mergedOptions: SceneSerializeOptions = {
      includeEntities: options.includeEntities !== undefined ? options.includeEntities : true,
      includeComponents: options.includeComponents !== undefined ? options.includeComponents : true,
      includeSkybox: options.includeSkybox !== undefined ? options.includeSkybox : true,
      includeAmbientLight: options.includeAmbientLight !== undefined ? options.includeAmbientLight : true,
      includeFog: options.includeFog !== undefined ? options.includeFog : true,
      prettyPrint: options.prettyPrint !== undefined ? options.prettyPrint : false,
      includeMetadata: options.includeMetadata !== undefined ? options.includeMetadata : true,
      metadata: options.metadata || {}
    };

    // 创建序列化数据
    const data: SceneSerializedData = {
      version: SceneSerializer.VERSION,
      id: scene.id,
      name: scene.name
    };

    // 序列化实体
    if (mergedOptions.includeEntities) {
      data.entities = this.serializeEntities(scene, mergedOptions);
    }

    // 序列化天空盒
    if (mergedOptions.includeSkybox) {
      const skybox = scene.getSkybox();

      if (skybox) {
        data.skybox = this.serializeSkybox(skybox);
      }
    }

    // 序列化环境光
    if (mergedOptions.includeAmbientLight) {
      const ambientLight = scene.getAmbientLight();

      if (ambientLight) {
        data.ambientLight = this.serializeAmbientLight(ambientLight);
      }
    }

    // 序列化雾效
    if (mergedOptions.includeFog) {
      const threeScene = scene.getThreeScene();

      if (threeScene.fog) {
        // 使用类型断言处理 FogBase 类型
        data.fog = this.serializeFog(threeScene.fog as (THREE.Fog | THREE.FogExp2));
      }
    }

    // 添加元数据
    if (mergedOptions.includeMetadata) {
      data.metadata = {
        ...mergedOptions.metadata,
        serializeDate: new Date().toISOString()
      };
    }

    return data;
  }

  /**
   * 序列化实体
   * @param scene 场景实例
   * @param options 序列化选项
   * @returns 实体数据数组
   */
  private serializeEntities(scene: Scene, options: SceneSerializeOptions): any[] {
    const entities = scene.getEntities();
    const result: any[] = [];

    for (const entity of entities) {
      const entityData: any = {
        id: entity.id,
        name: entity.name,
        active: entity.isActive(),
        tags: entity.getTags()
      };

      // 序列化组件
      if (options.includeComponents) {
        entityData.components = this.serializeComponents(entity);
      }

      result.push(entityData);
    }

    return result;
  }

  /**
   * 序列化组件
   * @param entity 实体实例
   * @returns 组件数据数组
   */
  private serializeComponents(entity: Entity): any[] {
    const components = entity.getAllComponents();
    const result: any[] = [];

    for (const component of components) {
      const componentType = component.getType();

      // 创建组件数据
      const componentData: any = {
        type: componentType
      };

      // 使用组件序列化器
      const serializer = this.componentSerializers.get(componentType);

      if (serializer) {
        componentData.data = serializer(component);
      } else {
        // 默认序列化
        componentData.data = this.defaultSerializeComponent(component);
      }

      result.push(componentData);
    }

    return result;
  }

  /**
   * 默认序列化组件
   * @param component 组件实例
   * @returns 组件数据
   */
  private defaultSerializeComponent(component: Component): any {
    // 默认情况下，尝试将组件转换为普通对象
    const obj: any = {};

    // 获取组件的所有属性
    for (const key in component) {
      // 跳过私有属性和函数
      if (key.startsWith('_') || typeof (component as any)[key] === 'function') {
        continue;
      }

      // 获取属性值
      const value = (component as any)[key];

      // 序列化属性值
      obj[key] = this.serializeValue(value);
    }

    return obj;
  }

  /**
   * 序列化值
   * @param value 值
   * @returns 序列化后的值
   */
  private serializeValue(value: any): any {
    if (value === null || value === undefined) {
      return null;
    }

    // 处理基本类型
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return value;
    }

    // 处理数组
    if (Array.isArray(value)) {
      return value.map(item => this.serializeValue(item));
    }

    // 处理对象
    if (typeof value === 'object') {
      // 处理Three.js对象
      if (value instanceof THREE.Vector2) {
        return { type: 'Vector2', x: value.x, y: value.y };
      } else if (value instanceof THREE.Vector3) {
        return { type: 'Vector3', x: value.x, y: value.y, z: value.z };
      } else if (value instanceof THREE.Vector4) {
        return { type: 'Vector4', x: value.x, y: value.y, z: value.z, w: value.w };
      } else if (value instanceof THREE.Quaternion) {
        return { type: 'Quaternion', x: value.x, y: value.y, z: value.z, w: value.w };
      } else if (value instanceof THREE.Euler) {
        return { type: 'Euler', x: value.x, y: value.y, z: value.z, order: value.order };
      } else if (value instanceof THREE.Color) {
        return { type: 'Color', r: value.r, g: value.g, b: value.b };
      } else if (value instanceof THREE.Matrix3) {
        return { type: 'Matrix3', elements: [...value.elements] };
      } else if (value instanceof THREE.Matrix4) {
        return { type: 'Matrix4', elements: [...value.elements] };
      }

      // 处理普通对象
      const obj: any = {};

      for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
          obj[key] = this.serializeValue(value[key]);
        }
      }

      return obj;
    }

    // 无法序列化的值
    return null;
  }

  /**
   * 序列化天空盒
   * @param skybox 天空盒实例
   * @returns 天空盒数据
   */
  private serializeSkybox(skybox: Skybox): any {
    // 获取天空盒类型
    const type = skybox.getType();

    // 创建天空盒数据
    const skyboxData: any = {
      type
    };

    // 获取天空盒网格和材质
    const mesh = skybox.getMesh();
    if (!mesh) {
      return skyboxData;
    }

    // 根据类型序列化不同的数据
    switch (type) {
      case SkyboxType.CUBEMAP:
        // 获取立方体贴图
        if (mesh.material instanceof THREE.MeshBasicMaterial && mesh.material.envMap) {
          // 注意：无法直接获取原始URL，这里只能保存材质信息
          skyboxData.material = {
            type: 'MeshBasicMaterial',
            side: THREE.BackSide
          };
        }
        break;

      case SkyboxType.EQUIRECTANGULAR:
        // 获取等距矩形贴图
        if (mesh.material instanceof THREE.MeshBasicMaterial && mesh.material.map) {
          // 注意：无法直接获取原始URL，这里只能保存材质信息
          skyboxData.material = {
            type: 'MeshBasicMaterial',
            side: THREE.BackSide
          };
        }
        break;

      case SkyboxType.PROCEDURAL:
        // 获取程序化天空盒参数
        if (mesh.material instanceof THREE.ShaderMaterial) {
          const material = mesh.material;
          if (material.uniforms) {
            const topColor = material.uniforms.topColor?.value;
            const bottomColor = material.uniforms.bottomColor?.value;
            const exponent = material.uniforms.exponent?.value;

            skyboxData.proceduralParams = {
              topColor: topColor ? { r: topColor.r, g: topColor.g, b: topColor.b } : undefined,
              bottomColor: bottomColor ? { r: bottomColor.r, g: bottomColor.g, b: bottomColor.b } : undefined,
              exponent: exponent
            };
          }
        }
        break;
    }

    return skyboxData;
  }

  /**
   * 序列化环境光
   * @param ambientLight 环境光实例
   * @returns 环境光数据
   */
  private serializeAmbientLight(ambientLight: THREE.AmbientLight): any {
    return {
      color: {
        r: ambientLight.color.r,
        g: ambientLight.color.g,
        b: ambientLight.color.b
      },
      intensity: ambientLight.intensity
    };
  }

  /**
   * 序列化雾效
   * @param fog 雾效实例
   * @returns 雾效数据
   */
  private serializeFog(fog: THREE.Fog | THREE.FogExp2): any {
    // 使用类型断言确保类型安全
    const fogData: any = {
      type: fog instanceof THREE.FogExp2 ? 'exponential' : 'linear',
      color: {
        r: fog.color.r,
        g: fog.color.g,
        b: fog.color.b
      }
    };

    if (fog instanceof THREE.FogExp2) {
      fogData.density = fog.density;
    } else if (fog instanceof THREE.Fog) {
      fogData.near = fog.near;
      fogData.far = fog.far;
    }

    return fogData;
  }

  /**
   * 将场景序列化为对象
   * @param scene 场景实例
   * @param options 序列化选项
   * @returns 序列化数据对象
   */
  public serializeToObject(scene: Scene, options: SceneSerializeOptions = {}): SceneSerializedData {
    return this.serializeScene(scene, options);
  }

  /**
   * 将场景序列化为JSON字符串
   * @param scene 场景实例
   * @param options 序列化选项
   * @returns JSON字符串
   */
  public serializeToJSON(scene: Scene, options: SceneSerializeOptions = {}): string {
    const data = this.serializeScene(scene, options);

    return JSON.stringify(data, null, options.prettyPrint ? 2 : undefined);
  }

  /**
   * 反序列化场景
   * @param data 序列化数据
   * @param targetScene 目标场景
   * @param options 反序列化选项
   * @returns 反序列化后的场景
   */
  public deserializeScene(
    data: SceneSerializedData,
    targetScene?: Scene,
    options: SceneDeserializeOptions = {}
  ): Scene {
    // 检查版本兼容性
    if (!this.checkVersionCompatibility(data.version)) {
      console.warn(`场景数据版本 ${data.version} 可能与当前版本 ${SceneSerializer.VERSION} 不兼容`);
    }

    // 合并选项
    const mergedOptions: SceneDeserializeOptions = {
      includeEntities: options.includeEntities !== undefined ? options.includeEntities : true,
      includeComponents: options.includeComponents !== undefined ? options.includeComponents : true,
      includeSkybox: options.includeSkybox !== undefined ? options.includeSkybox : true,
      includeAmbientLight: options.includeAmbientLight !== undefined ? options.includeAmbientLight : true,
      includeFog: options.includeFog !== undefined ? options.includeFog : true,
      keepExistingEntities: options.keepExistingEntities !== undefined ? options.keepExistingEntities : false,
      mergeWithExisting: options.mergeWithExisting !== undefined ? options.mergeWithExisting : false,
      componentDeserializers: options.componentDeserializers || this.componentDeserializers
    };

    // 获取或创建目标场景
    let scene: Scene;

    if (targetScene) {
      scene = targetScene;

      // 更新场景属性
      scene.id = data.id;
      scene.name = data.name;

      // 如果不保留现有实体，则清空场景
      if (!mergedOptions.keepExistingEntities && !mergedOptions.mergeWithExisting) {
        scene.clear();
      }
    } else if (this.world) {
      // 创建新场景
      scene = this.world.createScene(data.name);
      scene.id = data.id;
    } else {
      // 创建独立场景
      scene = new Scene(data.name);
      scene.id = data.id;
    }

    // 反序列化实体
    if (mergedOptions.includeEntities && data.entities) {
      this.deserializeEntities(data.entities, scene, mergedOptions);
    }

    // 反序列化天空盒
    if (mergedOptions.includeSkybox && data.skybox) {
      const skybox = this.deserializeSkybox(data.skybox);

      if (skybox) {
        scene.setSkybox(skybox);
      }
    }

    // 反序列化环境光
    if (mergedOptions.includeAmbientLight && data.ambientLight) {
      this.deserializeAmbientLight(data.ambientLight, scene);
    }

    // 反序列化雾效
    if (mergedOptions.includeFog && data.fog) {
      this.deserializeFog(data.fog, scene);
    }

    return scene;
  }

  /**
   * 检查版本兼容性
   * @param version 版本字符串
   * @returns 是否兼容
   */
  private checkVersionCompatibility(version: string): boolean {
    // 解析版本号
    const currentParts = SceneSerializer.VERSION.split('.').map(Number);
    const dataParts = version.split('.').map(Number);

    // 检查主版本号
    if (currentParts[0] !== dataParts[0]) {
      return false;
    }

    // 检查次版本号
    if (currentParts[1] < dataParts[1]) {
      return false;
    }

    return true;
  }

  /**
   * 反序列化实体
   * @param entitiesData 实体数据数组
   * @param scene 场景实例
   * @param options 反序列化选项
   */
  private deserializeEntities(
    entitiesData: any[],
    scene: Scene,
    options: SceneDeserializeOptions
  ): void {
    for (const entityData of entitiesData) {
      // 创建实体
      let entity: Entity;

      if (this.world) {
        // 创建实体时先不设置ID，避免冲突
        entity = new Entity(entityData.name);
        // 安全地设置ID
        try {
          entity.id = entityData.id;
        } catch (error) {
          if (error.message.includes('not extensible') || error.message.includes('Cannot add property')) {
            console.warn('实体对象不可扩展，使用Object.defineProperty设置ID');
            Object.defineProperty(entity, 'id', {
              value: entityData.id,
              writable: true,
              enumerable: true,
              configurable: true
            });
          } else {
            throw error;
          }
        }
        // 然后添加到世界中
        this.world.addEntity(entity);
      } else {
        entity = new Entity(entityData.name);
        try {
          entity.id = entityData.id;
        } catch (error) {
          if (error.message.includes('not extensible') || error.message.includes('Cannot add property')) {
            console.warn('实体对象不可扩展，使用Object.defineProperty设置ID');
            Object.defineProperty(entity, 'id', {
              value: entityData.id,
              writable: true,
              enumerable: true,
              configurable: true
            });
          } else {
            throw error;
          }
        }
      }

      // 设置实体属性
      entity.setActive(entityData.active !== undefined ? entityData.active : true);

      // 设置标签
      if (entityData.tags) {
        for (const tag of entityData.tags) {
          entity.addTag(tag);
        }
      }

      // 反序列化组件
      if (options.includeComponents && entityData.components) {
        this.deserializeComponents(entityData.components, entity, options);
      }

      // 添加到场景
      scene.addEntity(entity);
    }
  }

  /**
   * 反序列化组件
   * @param componentsData 组件数据数组
   * @param entity 实体实例
   * @param options 反序列化选项
   */
  private deserializeComponents(
    componentsData: any[],
    entity: Entity,
    options: SceneDeserializeOptions
  ): void {
    for (const componentData of componentsData) {
      const componentType = componentData.type;

      // 使用组件反序列化器
      const deserializer = options.componentDeserializers?.get(componentType);

      if (deserializer) {
        deserializer(componentData.data, entity);
      } else {
        // 默认反序列化
        this.defaultDeserializeComponent(componentType, componentData.data, entity);
      }
    }
  }

  /**
   * 默认反序列化组件
   * @param componentType 组件类型
   * @param data 组件数据
   * @param entity 实体实例
   * @returns 组件实例
   */
  private defaultDeserializeComponent(
    componentType: string,
    data: any,
    entity: Entity
  ): Component | null {
    // 尝试获取现有组件
    let component = entity.getComponent(componentType);

    // 如果组件不存在，则尝试创建
    if (!component) {
      try {
        // 使用类型断言处理类型问题
        // 注意：这里假设 entity.addComponent 接受组件类型名称字符串
        // 实际实现可能需要根据组件注册表或工厂创建组件
        component = entity.addComponent(componentType as any);
      } catch (error) {
        console.warn(`无法创建组件类型: ${componentType}`, error);
        return null;
      }
    }

    // 如果组件创建失败，则返回null
    if (!component) {
      return null;
    }

    // 设置组件属性
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        try {
          (component as any)[key] = this.deserializeValue(data[key]);
        } catch (error) {
          console.warn(`无法设置组件属性: ${componentType}.${key}`, error);
        }
      }
    }

    return component;
  }

  /**
   * 反序列化值
   * @param value 序列化后的值
   * @returns 原始值
   */
  private deserializeValue(value: any): any {
    if (value === null || value === undefined) {
      return null;
    }

    // 处理基本类型
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return value;
    }

    // 处理数组
    if (Array.isArray(value)) {
      return value.map(item => this.deserializeValue(item));
    }

    // 处理对象
    if (typeof value === 'object') {
      // 处理Three.js对象
      if (value.type === 'Vector2') {
        return new THREE.Vector2(value.x, value.y);
      } else if (value.type === 'Vector3') {
        return new THREE.Vector3(value.x, value.y, value.z);
      } else if (value.type === 'Vector4') {
        return new THREE.Vector4(value.x, value.y, value.z, value.w);
      } else if (value.type === 'Quaternion') {
        return new THREE.Quaternion(value.x, value.y, value.z, value.w);
      } else if (value.type === 'Euler') {
        return new THREE.Euler(value.x, value.y, value.z, value.order);
      } else if (value.type === 'Color') {
        return new THREE.Color(value.r, value.g, value.b);
      } else if (value.type === 'Matrix3') {
        const matrix = new THREE.Matrix3();
        matrix.fromArray(value.elements);
        return matrix;
      } else if (value.type === 'Matrix4') {
        const matrix = new THREE.Matrix4();
        matrix.fromArray(value.elements);
        return matrix;
      }

      // 处理普通对象
      const obj: any = {};

      for (const key in value) {
        if (Object.prototype.hasOwnProperty.call(value, key)) {
          obj[key] = this.deserializeValue(value[key]);
        }
      }

      return obj;
    }

    return value;
  }

  /**
   * 反序列化天空盒
   * @param skyboxData 天空盒数据
   * @returns 天空盒实例
   */
  private deserializeSkybox(skyboxData: any): Skybox | null {
    const type = skyboxData.type;

    // 创建天空盒选项
    const options: any = {
      type: type as SkyboxType
    };

    // 根据类型设置不同的选项
    switch (type) {
      case SkyboxType.CUBEMAP:
        if (skyboxData.urls && Array.isArray(skyboxData.urls)) {
          options.cubemapPaths = skyboxData.urls;
        }
        break;

      case SkyboxType.EQUIRECTANGULAR:
        if (skyboxData.url) {
          options.equirectangularPath = skyboxData.url;
        }
        break;

      case SkyboxType.PROCEDURAL:
        if (skyboxData.proceduralParams) {
          options.proceduralParams = {};

          if (skyboxData.proceduralParams.topColor) {
            options.proceduralParams.topColor = new THREE.Color(
              skyboxData.proceduralParams.topColor.r,
              skyboxData.proceduralParams.topColor.g,
              skyboxData.proceduralParams.topColor.b
            );
          }

          if (skyboxData.proceduralParams.bottomColor) {
            options.proceduralParams.bottomColor = new THREE.Color(
              skyboxData.proceduralParams.bottomColor.r,
              skyboxData.proceduralParams.bottomColor.g,
              skyboxData.proceduralParams.bottomColor.b
            );
          }

          if (skyboxData.proceduralParams.exponent !== undefined) {
            options.proceduralParams.exponent = skyboxData.proceduralParams.exponent;
          }
        }
        break;

      default:
        return null;
    }

    // 创建天空盒
    return new Skybox(options);
  }

  /**
   * 反序列化环境光
   * @param ambientLightData 环境光数据
   * @param scene 场景实例
   */
  private deserializeAmbientLight(ambientLightData: any, scene: Scene): void {
    const color = new THREE.Color(
      ambientLightData.color.r,
      ambientLightData.color.g,
      ambientLightData.color.b
    );

    scene.setAmbientLight(color, ambientLightData.intensity);
  }

  /**
   * 反序列化雾效
   * @param fogData 雾效数据
   * @param scene 场景实例
   */
  private deserializeFog(fogData: any, scene: Scene): void {
    const color = new THREE.Color(
      fogData.color.r,
      fogData.color.g,
      fogData.color.b
    );

    if (fogData.type === 'exponential') {
      scene.setExponentialFog(color, fogData.density);
    } else {
      scene.setFog(color, fogData.near, fogData.far);
    }
  }

  /**
   * 反序列化单个实体
   * @param data 序列化数据
   * @param scene 目标场景
   * @param options 反序列化选项
   * @returns 反序列化后的实体
   */
  public deserializeEntity(
    data: SceneSerializedData,
    scene: Scene,
    options: SceneDeserializeOptions = {}
  ): Entity | null {
    // 检查版本兼容性
    if (!this.checkVersionCompatibility(data.version)) {
      console.warn(`场景数据版本 ${data.version} 可能与当前版本 ${SceneSerializer.VERSION} 不兼容`);
    }

    // 合并选项
    const mergedOptions: SceneDeserializeOptions = {
      includeEntities: options.includeEntities !== undefined ? options.includeEntities : true,
      includeComponents: options.includeComponents !== undefined ? options.includeComponents : true,
      includeSkybox: options.includeSkybox !== undefined ? options.includeSkybox : false,
      includeAmbientLight: options.includeAmbientLight !== undefined ? options.includeAmbientLight : false,
      includeFog: options.includeFog !== undefined ? options.includeFog : false,
      keepExistingEntities: options.keepExistingEntities !== undefined ? options.keepExistingEntities : true,
      mergeWithExisting: options.mergeWithExisting !== undefined ? options.mergeWithExisting : true,
      componentDeserializers: options.componentDeserializers || this.componentDeserializers
    };

    // 反序列化实体
    if (mergedOptions.includeEntities && data.entities && data.entities.length > 0) {
      // 取第一个实体作为根实体
      const entityData = data.entities[0];

      // 创建实体
      let entity: Entity;

      if (this.world) {
        // 创建实体时先不设置ID，避免冲突
        entity = new Entity(entityData.name);
        // 安全地设置ID
        try {
          entity.id = entityData.id;
        } catch (error) {
          if (error.message.includes('not extensible') || error.message.includes('Cannot add property')) {
            console.warn('实体对象不可扩展，使用Object.defineProperty设置ID');
            Object.defineProperty(entity, 'id', {
              value: entityData.id,
              writable: true,
              enumerable: true,
              configurable: true
            });
          } else {
            throw error;
          }
        }
        // 然后添加到世界中
        this.world.addEntity(entity);
      } else {
        entity = new Entity(entityData.name);
        try {
          entity.id = entityData.id;
        } catch (error) {
          if (error.message.includes('not extensible') || error.message.includes('Cannot add property')) {
            console.warn('实体对象不可扩展，使用Object.defineProperty设置ID');
            Object.defineProperty(entity, 'id', {
              value: entityData.id,
              writable: true,
              enumerable: true,
              configurable: true
            });
          } else {
            throw error;
          }
        }
      }

      // 设置实体属性
      entity.setActive(entityData.active !== undefined ? entityData.active : true);

      // 设置标签
      if (entityData.tags) {
        for (const tag of entityData.tags) {
          entity.addTag(tag);
        }
      }

      // 反序列化组件
      if (mergedOptions.includeComponents && entityData.components) {
        this.deserializeComponents(entityData.components, entity, mergedOptions);
      }

      // 添加到场景
      scene.addEntity(entity);

      return entity;
    }

    return null;
  }

  /**
   * 从JSON字符串反序列化场景
   * @param json JSON字符串
   * @param targetScene 目标场景
   * @param options 反序列化选项
   * @returns 反序列化后的场景
   */
  public deserializeFromJSON(
    json: string,
    targetScene?: Scene,
    options: SceneDeserializeOptions = {}
  ): Scene {
    const data = JSON.parse(json) as SceneSerializedData;

    return this.deserializeScene(data, targetScene, options);
  }

  /**
   * 设置世界实例
   * @param world 世界实例
   */
  public setWorld(world: World): void {
    this.world = world;
  }

  /**
   * 获取世界实例
   * @returns 世界实例
   */
  public getWorld(): World | null {
    return this.world;
  }

  /**
   * 销毁场景序列化器
   */
  public dispose(): void {
    // 清空组件序列化器和反序列化器
    this.componentSerializers.clear();
    this.componentDeserializers.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
