<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>场景加载测试</h1>
        <p>此页面用于测试修复后的场景加载功能，验证"Cannot add property id, object is not extensible"错误是否已解决。</p>

        <div class="test-section">
            <h3>测试1: 基本实体创建</h3>
            <button onclick="testBasicEntityCreation()">测试基本实体创建</button>
            <div id="test1-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 场景反序列化</h3>
            <button onclick="testSceneDeserialization()">测试场景反序列化</button>
            <div id="test2-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 实体ID冲突处理</h3>
            <button onclick="testEntityIdConflict()">测试ID冲突处理</button>
            <div id="test3-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 冻结对象处理</h3>
            <button onclick="testFrozenObjectHandling()">测试冻结对象处理</button>
            <div id="test4-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>测试5: 真实场景加载模拟</h3>
            <button onclick="testRealSceneLoading()">模拟真实场景加载</button>
            <div id="test5-log" class="log"></div>
        </div>
    </div>

    <script type="module">
        // 导入引擎模块
        import { Engine } from './src/libs/dl-engine.js';

        let engine = null;
        let world = null;

        // 初始化引擎
        async function initEngine() {
            try {
                engine = new Engine();
                await engine.initialize();
                world = engine.getWorld();
                log('engine-init', '引擎初始化成功', 'success');
                return true;
            } catch (error) {
                log('engine-init', `引擎初始化失败: ${error.message}`, 'error');
                return false;
            }
        }

        // 日志函数
        function log(testId, message, type = 'info') {
            const logElement = document.getElementById(`${testId}-log`);
            if (logElement) {
                const timestamp = new Date().toLocaleTimeString();
                const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
                logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        // 测试1: 基本实体创建
        window.testBasicEntityCreation = async function() {
            const testId = 'test1';
            log(testId, '开始测试基本实体创建...');
            
            if (!engine && !(await initEngine())) {
                return;
            }

            try {
                // 创建实体
                const entity = world.createEntity('测试实体');
                log(testId, `实体创建成功，ID: ${entity.id}`, 'success');

                // 验证实体属性
                if (entity.id && entity.name === '测试实体') {
                    log(testId, '实体属性验证通过', 'success');
                } else {
                    log(testId, '实体属性验证失败', 'error');
                }

                // 添加组件
                const transform = entity.getTransform();
                if (transform) {
                    log(testId, 'Transform组件获取成功', 'success');
                } else {
                    log(testId, 'Transform组件获取失败', 'error');
                }

            } catch (error) {
                log(testId, `测试失败: ${error.message}`, 'error');
                console.error('基本实体创建测试失败:', error);
            }
        };

        // 测试2: 场景反序列化
        window.testSceneDeserialization = async function() {
            const testId = 'test2';
            log(testId, '开始测试场景反序列化...');
            
            if (!engine && !(await initEngine())) {
                return;
            }

            try {
                // 创建测试场景数据
                const sceneData = {
                    version: "1.0.0",
                    id: "test-scene-001",
                    name: "测试场景",
                    entities: [
                        {
                            id: "entity-001",
                            name: "测试实体1",
                            active: true,
                            tags: ["test"],
                            components: [
                                {
                                    type: "Transform",
                                    properties: {
                                        position: { x: 0, y: 0, z: 0 },
                                        rotation: { x: 0, y: 0, z: 0, w: 1 },
                                        scale: { x: 1, y: 1, z: 1 }
                                    }
                                }
                            ]
                        },
                        {
                            id: "entity-002",
                            name: "测试实体2",
                            active: true,
                            tags: ["test", "second"],
                            components: []
                        }
                    ]
                };

                log(testId, '测试场景数据创建完成');

                // 序列化为JSON
                const json = JSON.stringify(sceneData);
                log(testId, '场景数据序列化完成');

                // 创建场景序列化器
                const SceneSerializer = engine.SceneSerializer || window.SceneSerializer;
                if (!SceneSerializer) {
                    log(testId, '场景序列化器不可用', 'error');
                    return;
                }

                const serializer = new SceneSerializer();
                serializer.setWorld(world);

                // 反序列化场景
                const scene = serializer.deserializeFromJSON(json);
                log(testId, `场景反序列化成功，场景名称: ${scene.name}`, 'success');

                // 验证实体
                const entities = scene.getEntities();
                log(testId, `场景包含 ${entities.length} 个实体`, 'success');

                entities.forEach((entity, index) => {
                    log(testId, `实体${index + 1}: ID=${entity.id}, 名称=${entity.name}`, 'success');
                });

            } catch (error) {
                log(testId, `测试失败: ${error.message}`, 'error');
                console.error('场景反序列化测试失败:', error);
            }
        };

        // 测试3: 实体ID冲突处理
        window.testEntityIdConflict = async function() {
            const testId = 'test3';
            log(testId, '开始测试实体ID冲突处理...');
            
            if (!engine && !(await initEngine())) {
                return;
            }

            try {
                // 创建第一个实体
                const entity1 = world.createEntity('实体1');
                log(testId, `第一个实体创建成功，ID: ${entity1.id}`);

                // 尝试创建具有相同ID的实体
                const entity2 = world.createEntity('实体2');
                
                // 手动设置相同的ID（这应该被安全处理）
                try {
                    entity2.id = entity1.id;
                    log(testId, '警告: ID冲突未被检测到', 'warning');
                } catch (error) {
                    log(testId, `ID冲突被正确处理: ${error.message}`, 'success');
                }

                // 验证两个实体有不同的ID
                if (entity1.id !== entity2.id) {
                    log(testId, '实体ID唯一性验证通过', 'success');
                } else {
                    log(testId, '实体ID唯一性验证失败', 'error');
                }

            } catch (error) {
                log(testId, `测试失败: ${error.message}`, 'error');
                console.error('实体ID冲突测试失败:', error);
            }
        };

        // 测试4: 冻结对象处理
        window.testFrozenObjectHandling = async function() {
            const testId = 'test4';
            log(testId, '开始测试冻结对象处理...');

            if (!engine && !(await initEngine())) {
                return;
            }

            try {
                // 创建一个普通实体
                const normalEntity = world.createEntity('普通实体');
                log(testId, `普通实体创建成功，ID: ${normalEntity.id}`);

                // 创建一个实体并冻结它
                const entityToFreeze = world.createEntity('待冻结实体');
                log(testId, `待冻结实体创建成功，ID: ${entityToFreeze.id}`);

                // 冻结实体对象
                Object.freeze(entityToFreeze);
                log(testId, '实体对象已冻结');

                // 尝试修改冻结的实体（这应该被安全处理）
                try {
                    entityToFreeze.newProperty = 'test';
                    log(testId, '警告: 冻结对象修改未被阻止', 'warning');
                } catch (error) {
                    log(testId, `冻结对象修改被正确阻止: ${error.message}`, 'success');
                }

                // 测试添加冻结实体到世界（这应该被安全处理）
                try {
                    const frozenEntity = Object.freeze({ name: '冻结实体' });
                    const result = world.addEntity(frozenEntity);
                    log(testId, `冻结实体添加成功，ID: ${result.id}（使用了安全处理）`, 'success');
                } catch (error) {
                    if (error.message.includes('not extensible')) {
                        log(testId, `冻结实体处理失败，需要进一步修复: ${error.message}`, 'error');
                    } else {
                        log(testId, `其他错误: ${error.message}`, 'error');
                    }
                }

                // 测试场景反序列化中的冻结对象处理
                try {
                    const frozenSceneData = Object.freeze({
                        version: "1.0.0",
                        id: "frozen-scene",
                        name: "冻结场景",
                        entities: Object.freeze([
                            Object.freeze({
                                id: "frozen-entity-001",
                                name: "冻结实体",
                                active: true,
                                tags: Object.freeze(["frozen"]),
                                components: Object.freeze([])
                            })
                        ])
                    });

                    const json = JSON.stringify(frozenSceneData);
                    log(testId, '冻结场景数据创建完成');

                    // 尝试反序列化冻结的场景数据
                    const SceneSerializer = engine.SceneSerializer || window.SceneSerializer;
                    if (SceneSerializer) {
                        const serializer = new SceneSerializer();
                        serializer.setWorld(world);
                        const scene = serializer.deserializeFromJSON(json);
                        log(testId, `冻结场景反序列化成功: ${scene.name}`, 'success');
                    } else {
                        log(testId, '场景序列化器不可用，跳过冻结场景测试', 'warning');
                    }
                } catch (error) {
                    log(testId, `冻结场景反序列化测试失败: ${error.message}`, 'error');
                }

            } catch (error) {
                log(testId, `测试失败: ${error.message}`, 'error');
                console.error('冻结对象处理测试失败:', error);
            }
        };

        // 测试5: 真实场景加载模拟
        window.testRealSceneLoading = async function() {
            const testId = 'test5';
            log(testId, '开始模拟真实场景加载...');

            if (!engine && !(await initEngine())) {
                return;
            }

            try {
                // 模拟从服务器获取的复杂场景数据
                const complexSceneData = {
                    version: "1.0.0",
                    id: "complex-scene-001",
                    name: "复杂测试场景",
                    entities: [
                        {
                            id: "camera-entity",
                            name: "主相机",
                            active: true,
                            tags: ["camera", "main"],
                            components: [
                                {
                                    type: "Transform",
                                    properties: {
                                        position: { x: 0, y: 5, z: 10 },
                                        rotation: { x: 0, y: 0, z: 0, w: 1 },
                                        scale: { x: 1, y: 1, z: 1 }
                                    }
                                },
                                {
                                    type: "Camera",
                                    properties: {
                                        fov: 60,
                                        near: 0.1,
                                        far: 1000,
                                        aspect: 16/9
                                    }
                                }
                            ]
                        },
                        {
                            id: "light-entity",
                            name: "方向光",
                            active: true,
                            tags: ["light", "directional"],
                            components: [
                                {
                                    type: "Transform",
                                    properties: {
                                        position: { x: 10, y: 10, z: 10 },
                                        rotation: { x: -0.5, y: 0.5, z: 0, w: 0.7 },
                                        scale: { x: 1, y: 1, z: 1 }
                                    }
                                },
                                {
                                    type: "DirectionalLight",
                                    properties: {
                                        color: "#ffffff",
                                        intensity: 1.0,
                                        castShadow: true
                                    }
                                }
                            ]
                        },
                        {
                            id: "ground-entity",
                            name: "地面",
                            active: true,
                            tags: ["ground", "static"],
                            components: [
                                {
                                    type: "Transform",
                                    properties: {
                                        position: { x: 0, y: 0, z: 0 },
                                        rotation: { x: 0, y: 0, z: 0, w: 1 },
                                        scale: { x: 10, y: 1, z: 10 }
                                    }
                                },
                                {
                                    type: "MeshRenderer",
                                    properties: {
                                        geometry: "plane",
                                        material: "standard",
                                        color: "#808080"
                                    }
                                }
                            ]
                        }
                    ],
                    settings: {
                        backgroundColor: "#87CEEB",
                        ambientLight: {
                            color: "#404040",
                            intensity: 0.3
                        },
                        fog: {
                            enabled: true,
                            color: "#87CEEB",
                            near: 50,
                            far: 200
                        }
                    }
                };

                log(testId, '复杂场景数据创建完成');

                // 模拟EngineService.loadScene的调用
                try {
                    // 创建新场景
                    const scene = engine.world.createScene('复杂测试场景');
                    log(testId, `场景创建成功: ${scene.name}`);

                    // 模拟从数据加载实体
                    for (const entityData of complexSceneData.entities) {
                        log(testId, `正在加载实体: ${entityData.name}`);

                        // 使用修复后的实体创建方法
                        const entity = new engine.Entity(entityData.name);

                        // 安全设置ID
                        try {
                            entity.id = entityData.id;
                        } catch (error) {
                            if (error.message.includes('not extensible')) {
                                Object.defineProperty(entity, 'id', {
                                    value: entityData.id,
                                    writable: true,
                                    enumerable: true,
                                    configurable: true
                                });
                            } else {
                                throw error;
                            }
                        }

                        // 添加到场景
                        scene.addEntity(entity);

                        // 设置标签
                        if (entityData.tags) {
                            entityData.tags.forEach(tag => entity.addTag(tag));
                        }

                        log(testId, `实体 ${entityData.name} 加载成功，ID: ${entity.id}`, 'success');
                    }

                    // 验证场景加载结果
                    const entities = scene.getEntities();
                    log(testId, `场景加载完成，包含 ${entities.length} 个实体`, 'success');

                    // 验证每个实体的属性
                    entities.forEach((entity, index) => {
                        log(testId, `实体${index + 1}: ${entity.name} (ID: ${entity.id})`, 'success');
                    });

                    log(testId, '真实场景加载模拟测试完成', 'success');

                } catch (error) {
                    log(testId, `场景加载失败: ${error.message}`, 'error');
                    console.error('场景加载错误:', error);
                }

            } catch (error) {
                log(testId, `测试失败: ${error.message}`, 'error');
                console.error('真实场景加载测试失败:', error);
            }
        };

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            log('engine-init', '正在初始化引擎...');
            await initEngine();
        });
    </script>
</body>
</html>
