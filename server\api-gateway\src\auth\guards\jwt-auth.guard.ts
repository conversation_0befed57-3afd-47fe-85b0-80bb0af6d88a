/**
 * JWT认证守卫
 */
import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    console.log(`JwtAuthGuard.canActivate 被调用: ${request.method} ${request.url}`);

    // 检查是否标记为公开路由
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    console.log(`路由 ${request.url} 是否为公开路由: ${isPublic}`);

    if (isPublic) {
      console.log(`跳过认证，允许访问公开路由: ${request.url}`);
      return true;
    }

    console.log(`需要JWT认证: ${request.url}`);
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any) {
    if (err || !user) {
      throw err || new UnauthorizedException('无效的认证令牌');
    }
    return user;
  }
}
