/**
 * API网关入口文件
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as compression from 'compression';
import * as helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { Reflector } from '@nestjs/core';
// import { RateLimiterGuard } from '@shared/rate-limiter';
// import { CircuitBreakerInterceptor } from '@shared/circuit-breaker';

async function bootstrap() {
  // 创建Nest应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 全局前缀
  app.setGlobalPrefix('api');

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 全局过滤器
  app.useGlobalFilters(new HttpExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
    // new CircuitBreakerInterceptor(app.get('Reflector'), app.get('CircuitBreakerService')),
  );

  // 全局守卫 - 暂时禁用
  // app.useGlobalGuards(new JwtAuthGuard(app.get(Reflector)));
  // app.useGlobalGuards(new RateLimiterGuard(app.get('Reflector'), app.get('RateLimiterService')));

  // 启用CORS
  const corsOrigins = configService.get<string>('CORS_ORIGIN', 'http://localhost,http://localhost:80,http://localhost:5173,http://localhost:3000').split(',');
  const corsMethods = configService.get<string>('CORS_METHODS', 'GET,POST,PUT,DELETE,PATCH,OPTIONS').split(',');
  const corsHeaders = configService.get<string>('CORS_HEADERS', 'Content-Type,Authorization,X-Requested-With,X-Request-ID').split(',');

  app.enableCors({
    origin: (origin, callback) => {
      // 允许没有origin的请求（如移动应用、Postman等）
      if (!origin) return callback(null, true);

      // 检查origin是否在允许列表中
      if (corsOrigins.includes(origin) || corsOrigins.includes('*')) {
        return callback(null, true);
      }

      // 开发环境允许localhost的任何端口
      if (process.env.NODE_ENV === 'development' && origin.includes('localhost')) {
        return callback(null, true);
      }

      return callback(new Error('CORS policy violation'), false);
    },
    credentials: true,
    methods: corsMethods,
    allowedHeaders: corsHeaders,
    exposedHeaders: ['X-Request-ID'],
    optionsSuccessStatus: 200, // 支持旧版浏览器
    preflightContinue: false,
  });

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet.default());

  // Swagger文档
  const config = new DocumentBuilder()
    .setTitle('DL（Digital Learning）引擎API')
    .setDescription('DL（Digital Learning）引擎API文档')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // 启动HTTP服务
  const port = configService.get<number>('PORT') || configService.get<number>('API_GATEWAY_PORT', 3000);

  // 打印所有注册的路由
  const server = app.getHttpAdapter();
  const router = server.getInstance()._router;
  console.log('注册的路由:');
  if (router && router.stack) {
    router.stack.forEach((layer: any) => {
      if (layer.route) {
        const methods = Object.keys(layer.route.methods).join(', ').toUpperCase();
        console.log(`${methods} ${layer.route.path}`);
      }
    });
  }

  await app.listen(port);
  console.log(`API网关已启动，端口: ${port}`);
}

bootstrap();
